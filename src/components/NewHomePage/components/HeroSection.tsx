import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import { Button } from "@/components/UI/Button";
import Marquee from "react-fast-marquee";
import {
  HeadingSmall,
  HeadingXLarge,
  HeadingXLarge2,
} from "@/components/UI/Typography";
import Image from "next/image";
import { orufy<PERSON>and<PERSON> } from "@/utils/orufyHandler";
import { useSessionStorage } from "usehooks-ts";
import { useState } from "react";
import GetYourPlansModal from "./GetYourPlansModal";
import homePageData from "./data";
import { htmlParser } from "@/utils/htmlParser";
import SectionContainer from "@/components/globals/SectionContainer";

export type HeroSectionProps = {
  title: string;
  spanText: string;
  subtitle: string;
  marqueeImages: string[];
};
const HeroSection = ({
  title,
  spanText,
  subtitle,
  marqueeImages,
}: HeroSectionProps) => {
  // utms
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);
  const [openModal, setOpenModal] = useState(false);

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const images = marqueeImages;

  function bookACall() {
    const orufyLink = `${process.env.NEXT_PUBLIC_ONE_ASSURE_ORUFY_LINK}?utm_source=${utm_source}&utm_medium=${utm_medium}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`;
    orufyHandler(orufyLink);
  }

  // return (
  //   <div className="flex flex-col bg-primary-100 pt-6 pb-32 md:flex-row md:mt-[-122px] md:pt-[122px] md:pb-[250px] relative md:mb-56  overflow-x-clip md:h-[790px]">
  //     <SectionContainerLarge className="flex flex-col md:flex-row mt-8 md:mt-20 z-10">
  //       <div className="w-full md:w-[50%] px-4 md:px-0">
  //         <HeadingXLarge className="text-neutral-1100 font-medium mb-8 ">
  //           {htmlParser(title, {
  //             components: {
  //               p: HeadingXLarge2,
  //               em: HeadingXLarge2,
  //             },
  //             classNames: {
  //               p: "text-neutral-1100",
  //               em: "text-secondary-400",
  //             },
  //           })}
  //         </HeadingXLarge>
  //         <HeadingSmall className="font-medium text-neutral-1100 mb-8 ">
  //           {subtitle}
  //         </HeadingSmall>

  //         <div className="flex mb-12 md:mb-0 flex-row gap-4 sm:gap-8">
  //           <div
  //             className="relative inline-block p-0.5 hover:cursor-pointer"
  //             onClick={bookACall}
  //           >
  //             <div
  //               style={{
  //                 filter: "url(#squiggly)",
  //               }}
  //               className="absolute inset-0 border-4 border-primary-400 rounded-xl z-10"
  //             />
  //             <Button
  //               variant="primary"
  //               className="w-full sm:w-auto px-4 sm:px-6 py-4 sm:py-6 text-sm sm:text-base font-normal rounded-xl hover:scale-100"
  //             >
  //               Talk To A Human
  //             </Button>
  //           </div>

  //           <div className="relative inline-block p-0.5 hover:cursor-pointer">
  //             <div
  //               onClick={() => setOpenModal(true)}
  //               style={{
  //                 filter: "url(#squiggly)",
  //               }}
  //               className="absolute inset-0 border-4 border-primary-800 rounded-xl z-10"
  //             />
  //             <Button
  //               variant="secondary"
  //               className="w-full sm:w-auto px-4 sm:px-6 py-4 sm:py-6 text-sm sm:text-base font-normal rounded-xl hover:scale-100 bg-white text-primary-800"
  //             >
  //               Get Your Plan
  //             </Button>
  //           </div>
  //         </div>
  //       </div>
  //       <div className="w-full md:w-[50%]"></div>
  //     </SectionContainerLarge>
  //     <Image
  //       src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5b50-6616-75e0-b6e3-546efbf4ecdd/1345a0c4af468729669e2735d97454901ea0ed7a.png"
  //       alt=""
  //       width={1215}
  //       height={861}
  //       className="absolute bottom-[-36px] md:bottom-[-125px] left-1/2 -translate-x-[40%] z-0 w-auto"
  //     />

  //     <SectionContainerLarge className="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 !mb-0 border border-primary-300 rounded-xl bg-white p-2 md:p-8 ">
  //       <div>
  //       <HeadingSmall className="font-medium text-center text-primary-800 mb-4 md:mb-8">
  //         Backed By India's Most Trusted Insurers
  //       </HeadingSmall>
  //       <Marquee className="overflow-hidden" speed={30} gradient={false}>
  //         {images.map((src, index) => (
  //           <div
  //             key={index}
  //             className="relative w-16 md:w-20 h-8 md:h-10 bg-white mx-4"
  //           >
  //             <Image
  //               src={src}
  //               alt={`Insurance partner ${index + 1}`}
  //               className="object-contain"
  //               fill
  //             />
  //           </div>
  //         ))}
  //       </Marquee>
  //       </div>
  //     </SectionContainerLarge>
  //     {openModal && (
  //       <div
  //         className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
  //         onClick={handleCloseModal}
  //       >
  //         <GetYourPlansModal
  //           onClose={handleCloseModal}
  //           title={homePageData.getYourPlanes.title}
  //           subtitle={homePageData.getYourPlanes.subtitle}
  //           insuranceOptions={homePageData.getYourPlanes.insuranceOptions}
  //           insurerOptions={homePageData.getYourPlanes.insurerOptions}
  //           policyOptions={homePageData.getYourPlanes.policyOptions}
  //           buttonText="Get Your Plans"
  //         />
  //       </div>
  //     )}
  //   </div>
  // );

  return (
    <div className="mt-[-122px] relative overflow-hidden">
      <div className="bg-primary-100 pt-[122px]">
        <SectionContainer className="flex relative pt-28 pb-64 md:!px-4">
          <div className="w-full md:w-1/2 flex flex-col gap-8 items-center md:items-start">
            <HeadingXLarge className="text-neutral-1100 font-medium text-center md:text-left">
              {htmlParser(title, {
                components: {
                  p: HeadingXLarge2,
                  em: HeadingXLarge2,
                },
                classNames: {
                  p: "text-neutral-1100",
                  em: "text-secondary-400",
                },
              })}
            </HeadingXLarge>
            <HeadingSmall className="font-medium text-neutral-1100 text-center md:text-left">
              {subtitle}
            </HeadingSmall>
            <div className="flex gap-4 md:gap-8">
              <div
                className="relative inline-block p-0.5 hover:cursor-pointer"
                onClick={bookACall}
              >
                <div
                  style={{
                    filter: "url(#squiggly)",
                  }}
                  className="absolute inset-0 border-4 border-primary-400 rounded-xl z-10"
                />
                <Button
                  variant="primary"
                  className="px-4 sm:px-6 py-4 sm:py-6 rounded-xl hover:scale-100"
                >
                  Talk To A Human
                </Button>
              </div>

              <div className="relative inline-block p-0.5 hover:cursor-pointer">
                <div
                  onClick={() => setOpenModal(true)}
                  style={{
                    filter: "url(#squiggly)",
                  }}
                  className="absolute inset-0 border-4 border-primary-800 rounded-xl z-10"
                />
                <Button
                  variant="secondary"
                  className="px-4 sm:px-6 py-4 sm:py-6 rounded-xl hover:scale-100 bg-white text-primary-800"
                >
                  Get Your Plan
                </Button>
              </div>
            </div>
          </div>
          <Image
            // src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5b50-6616-75e0-b6e3-546efbf4ecdd/1345a0c4af468729669e2735d97454901ea0ed7a.png"
            src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a72b4-4a79-7de9-930b-6dde3d2f361d/e9039401e80d51e74132a5592865b132e9724cb4(1).png"
            alt=""
            width={1215}
            height={861}
            className="absolute bottom-0 left-1/2 -translate-x-[40%] z-0 w-auto"
          />
        </SectionContainer>
      </div>
      <SectionContainerLarge className="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 border border-primary-300 rounded-xl bg-white py-8 !w-[calc(100%-2rem)] !px-0">
        <div>
          <HeadingSmall className="font-medium text-center text-primary-800 mb-4 md:mb-8">
            Backed By India's Most Trusted Insurers
          </HeadingSmall>
          <Marquee className="overflow-hidden" speed={30} gradient={false}>
            {images.map((src, index) => (
              <div
                key={index}
                className="relative w-16 md:w-20 h-8 md:h-10 bg-white mx-4"
              >
                <Image
                  src={src}
                  alt={`Insurance partner ${index + 1}`}
                  className="object-contain"
                  fill
                />
              </div>
            ))}
          </Marquee>
        </div>
      </SectionContainerLarge>
      {openModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={handleCloseModal}
        >
          <GetYourPlansModal
            onClose={handleCloseModal}
            title={homePageData.getYourPlanes.title}
            subtitle={homePageData.getYourPlanes.subtitle}
            insuranceOptions={homePageData.getYourPlanes.insuranceOptions}
            insurerOptions={homePageData.getYourPlanes.insurerOptions}
            policyOptions={homePageData.getYourPlanes.policyOptions}
            buttonText="Get Your Plans"
          />
        </div>
      )}
    </div>
  );
};

export default HeroSection;
